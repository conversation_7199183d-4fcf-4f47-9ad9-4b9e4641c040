import { TFunction } from 'i18next';
import { z } from 'zod';

/**
 * Login form values interface
 */
export interface LoginFormValues {
  username: string;
  password: string;
  rememberMe: boolean;
  recaptchaToken?: string;
}

/**
 * Register form values interface
 */
export interface RegisterFormValues {
  fullName: string;
  email: string;
  phone: string;
  password: string;
  recaptchaToken?: string;
}

/**
 * Company register form values interface
 */
export interface CompanyRegisterFormValues {
  companyName: string;
  taxCode: string;
  companyEmail: string;
  password: string;
  confirmPassword: string;
  phoneNumber: string;
  address: string;
  recaptchaToken?: string;
}

/**
 * Forgot password form values interface
 */
export interface ForgotPasswordFormValues {
  emailOrPhone: string;
}

/**
 * Create login schema with translations
 */
export const createLoginSchema = (t: TFunction) => {
  return z.object({
    username: z
      .string()
      .min(1, t('validation.required', { field: t('auth.email') }))
      .email(t('validation.email')),
    password: z
      .string()
      .min(1, t('validation.required', { field: t('auth.password') }))
      .min(6, t('validation.minLength', { field: t('auth.password'), length: 6 })),
    rememberMe: z.boolean().optional(),
    recaptchaToken: z.string().optional().nullable(),
  });
};

/**
 * Create register schema with translations
 */
export const createRegisterSchema = (t: TFunction) => {
  return z.object({
    fullName: z
      .string()
      .min(1, t('validation.required', { field: t('auth.fullName') }))
      .min(2, t('validation.minLength', { field: t('auth.fullName'), length: 2 })),
    email: z
      .string()
      .min(1, t('validation.required', { field: t('auth.email') }))
      .email(t('validation.email')),
    phone: z
      .string()
      .min(1, t('validation.required', { field: t('auth.phone') }))
      .regex(/^\d{10,11}$/, t('validation.phone')),
    password: z
      .string()
      .min(1, t('validation.required', { field: t('auth.password') }))
      .min(8, t('validation.minLength', { field: t('auth.password'), length: 8 }))
      .regex(/[A-Z]/, t('validation.passwordUppercase'))
      .regex(/[a-z]/, t('validation.passwordLowercase'))
      .regex(/\d/, t('validation.passwordNumber'))
      .regex(/[^\dA-Za-z]/, t('validation.passwordSpecial')),
    recaptchaToken: z.string().optional(),
  });
};

/**
 * Create forgot password schema with translations
 */
export const createForgotPasswordSchema = (t: TFunction) => {
  return z.object({
    email: z
      .string()
      .min(1, t('validation.required', { field: t('auth.email') }))
      .email(t('validation.email')),
  });
};

/**
 * Reset password form values interface
 */
export interface ResetPasswordFormValues {
  password: string;
  confirmPassword: string;
}

/**
 * Create reset password schema with translations
 */
export const createResetPasswordSchema = (t: TFunction) => {
  return z
    .object({
      password: z
        .string()
        .min(1, t('validation.required', { field: t('auth.password') }))
        .min(8, t('validation.minLength', { field: t('auth.password'), length: 8 }))
        .regex(/[A-Z]/, t('validation.passwordUppercase'))
        .regex(/[a-z]/, t('validation.passwordLowercase'))
        .regex(/\d/, t('validation.passwordNumber'))
        .regex(/[^\dA-Za-z]/, t('validation.passwordSpecial')),
      confirmPassword: z
        .string()
        .min(1, t('validation.required', { field: t('auth.confirmPassword') })),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: t('validation.passwordsMatch'),
      path: ['confirmPassword'],
    });
};

/**
 * Create company register schema with translations
 */
export const createCompanyRegisterSchema = (t: TFunction) => {
  return z
    .object({
      companyName: z
        .string()
        .min(1, t('validation.required', { field: t('auth.companyName') }))
        .min(3, t('validation.minLength', { field: t('auth.companyName'), length: 3 }))
        .max(255, t('validation.maxLength', { field: t('auth.companyName'), length: 255 })),
      companyEmail: z
        .string()
        .min(1, t('validation.required', { field: t('auth.email') }))
        .email(t('validation.email')),
      password: z
        .string()
        .min(1, t('validation.required', { field: t('auth.password') }))
        .min(8, t('validation.minLength', { field: t('auth.password'), length: 8 }))
        .regex(/[A-Z]/, t('validation.passwordUppercase'))
        .regex(/[a-z]/, t('validation.passwordLowercase'))
        .regex(/\d/, t('validation.passwordNumber'))
        .regex(/[^\dA-Za-z]/, t('validation.passwordSpecial')),
      confirmPassword: z
        .string()
        .min(1, t('validation.required', { field: t('auth.confirmPassword') })),
      phoneNumber: z
        .string()
        .min(1, t('validation.required', { field: t('auth.phone') }))
        .regex(/^\d{10,15}$/, t('validation.phone')),
      recaptchaToken: z.string().optional(),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: t('validation.passwordsMatch', 'Mật khẩu và xác nhận mật khẩu không khớp'),
      path: ['confirmPassword'],
    });
};
